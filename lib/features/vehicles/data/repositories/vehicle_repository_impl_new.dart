import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';
import 'package:gaadi_sewa/core/error/failures.dart';

/// Implementation of VehicleRepository using Supabase as the data source
class VehicleRepositoryImpl implements VehicleRepository {
  final SupabaseClient _supabaseClient;

  VehicleRepositoryImpl({SupabaseClient? supabaseClient})
      : _supabaseClient = supabaseClient ?? Supabase.instance.client;

  @override
  Future<List<VehicleModel>> getVehicles({
    double? maxDistance,
    Position? userLocation,
    VehicleType? type,
    double? maxDailyRate,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _supabaseClient
          .from('vehicles')
          .select()
          .eq('is_available', true);
      
      // Apply filters
      if (type != null) {
        query = query.eq('type', type.toString().split('.').last);
      }
      if (maxDailyRate != null) {
        query = query.lte('daily_rate', maxDailyRate);
      }
      
      // Apply pagination
      query = query.range(offset, offset + limit - 1);
      
      final response = await query;
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      var vehicles = data.map((json) => VehicleModel.fromJson(json)).toList();
      
      // Apply distance filter if location is provided
      if (userLocation != null) {
        vehicles = vehicles.where((vehicle) {
          final distance = Geolocator.distanceBetween(
            userLocation.latitude,
            userLocation.longitude,
            vehicle.location.latitude,
            vehicle.location.longitude,
          ) / 1000; // Convert to kilometers
          
          vehicle.distanceFromUser = distance;
          return maxDistance == null || distance <= maxDistance;
        }).toList();
      }
      
      return vehicles;
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<List<VehicleModel>> getVehiclesAdvanced(VehicleFilterModel filter) async {
    try {
      var query = _supabaseClient
          .from('vehicles')
          .select()
          .eq('is_available', true);
      
      // Apply filters
      if (filter.type != null) {
        query = query.eq('type', filter.type.toString().split('.').last);
      }
      if (filter.make != null) {
        query = query.eq('make', filter.make);
      }
      if (filter.model != null) {
        query = query.eq('model', filter.model);
      }
      if (filter.minYear != null) {
        query = query.gte('year', filter.minYear);
      }
      if (filter.maxYear != null) {
        query = query.lte('year', filter.maxYear);
      }
      if (filter.minPrice != null) {
        query = query.gte('daily_rate', filter.minPrice);
      }
      if (filter.maxPrice != null) {
        query = query.lte('daily_rate', filter.maxPrice);
      }
      if (filter.features?.isNotEmpty ?? false) {
        query = query.contains('features', filter.features!);
      }
      
      // Apply sorting
      if (filter.sortBy != null) {
        final ascending = filter.sortOrder == SortOrder.ascending;
        switch (filter.sortBy) {
          case VehicleSortBy.price:
            query = query.order('daily_rate', ascending: ascending);
            break;
          case VehicleSortBy.year:
            query = query.order('year', ascending: !ascending);
            break;
          case VehicleSortBy.rating:
            query = query.order('avg_rating', ascending: !ascending);
            break;
        }
      }
      
      // Apply pagination
      query = query.range(filter.offset, filter.offset + filter.limit - 1);
      
      final response = await query;
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      var vehicles = data.map((json) => VehicleModel.fromJson(json)).toList();
      
      // Apply distance filter if location is provided
      if (filter.userLocation != null && filter.maxDistance != null) {
        vehicles = vehicles.where((vehicle) {
          final distance = Geolocator.distanceBetween(
            filter.userLocation!.latitude,
            filter.userLocation!.longitude,
            vehicle.location.latitude,
            vehicle.location.longitude,
          ) / 1000; // Convert to kilometers
          
          vehicle.distanceFromUser = distance;
          return distance <= filter.maxDistance!;
        }).toList();
      }
      
      return vehicles;
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<List<VehicleModel>> searchVehicles(String query, {int limit = 20, int offset = 0}) async {
    try {
      final searchQuery = '%$query%';
      final response = await _supabaseClient
          .from('vehicles')
          .select()
          .or('make.ilike.$searchQuery,model.ilike.$searchQuery,description.ilike.$searchQuery')
          .range(offset, offset + limit - 1);
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => VehicleModel.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<VehicleModel?> getVehicleById(String id) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select()
          .eq('id', id)
          .single();
      
      if (response is PostgrestError) {
        if (response.code == 'PGRST116') { // Not found
          return null;
        }
        throw ServerFailure(message: response.message);
      }
      
      return VehicleModel.fromJson(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<List<VehicleModel>> getVehiclesByOwner(String ownerId) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select()
          .eq('owner_id', ownerId)
          .order('created_at', ascending: false);
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => VehicleModel.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<VehicleModel> createVehicle(VehicleModel vehicle) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .insert(vehicle.toJson())
          .select()
          .single();
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      return VehicleModel.fromJson(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<VehicleModel> updateVehicle(VehicleModel vehicle) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .update(vehicle.toJson())
          .eq('id', vehicle.id)
          .select()
          .single();
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      return VehicleModel.fromJson(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<void> deleteVehicle(String id) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .delete()
          .eq('id', id);
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<bool> toggleAvailability(String id, bool isAvailable) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .update({'is_available': isAvailable})
          .eq('id', id)
          .select()
          .single();
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      return isAvailable;
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleMakes() async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('make')
          .order('make');
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      final makes = data.map((e) => e['make'].toString()).toSet().toList();
      return makes;
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleModels(String make) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('model')
          .eq('make', make)
          .order('model');
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      final models = data.map((e) => e['model'].toString()).toSet().toList();
      return models;
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleFeatures() async {
    try {
      final response = await _supabaseClient
          .from('vehicle_features')
          .select('name')
          .order('name');
      
      if (response is PostgrestError) {
        throw ServerFailure(message: response.message);
      }
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((e) => e['name'].toString()).toList();
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // Helper method to handle different types of errors
  Failure _handleError(dynamic error) {
    if (error is PostgrestError) {
      return ServerFailure(message: error.message);
    } else if (error is Failure) {
      return error;
    } else if (error is FormatException) {
      return ServerFailure(message: 'Data format error: ${error.message}');
    } else {
      return ServerFailure(message: 'Unexpected error: ${error.toString()}');
    }
  }
}
