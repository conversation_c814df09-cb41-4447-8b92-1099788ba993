import 'dart:async';
import 'dart:developer' as developer;

import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/vehicles/data/models/vehicle_model_extension.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';
import 'package:geolocator/geolocator.dart';
import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

/// Helper class for handling Supabase errors
class SupabaseErrorHandler {
  static Failure handleError(dynamic error) {
    developer.log('Supabase Error: $error', error: error, stackTrace: StackTrace.current);
    
    if (error is supabase.PostgrestException) {
      return ServerFailure(
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
      );
    } else if (error is NetworkException) {
      return NetworkFailure(message: error.message);
    } else if (error is Exception) {
      return ServerFailure(message: error.toString());
    } else {
      return const ServerFailure(message: 'An unknown error occurred');
    }
  }
  
  static NotFoundFailure notFound(String message) {
    return NotFoundFailure(message);
  }
}

// Helper methods for building queries
Map<String, dynamic> _buildVehicleQuery() {
  return {
    'select': '''
      *,
      owner:profiles!owner_id(
        id,
        full_name,
        avatar_url,
        rating,
        created_at
      ),
      (SELECT AVG(rating)::numeric(10,1) FROM reviews WHERE vehicle_id = vehicles.id) as avg_rating,
      (SELECT COUNT(*) FROM bookings WHERE vehicle_id = vehicles.id) as booking_count
    '''.replaceAll('\n', ' ').trim(),
    'order': 'created_at.desc',
  };
}

Map<String, dynamic> _applyPagination(Map<String, dynamic> query, {int? limit, int? offset}) {
  if (limit != null) {
    query['limit'] = limit;
  }
  if (offset != null) {
    query['offset'] = offset;
  }
  return query;
}

@LazySingleton(as: VehicleRepository)
class VehicleRepositoryImpl implements VehicleRepository {
  final supabase.SupabaseClient _supabaseClient;
  final NetworkInfo _networkInfo;
  final StreamController<List<VehicleModel>> _vehiclesController = 
      StreamController.broadcast();

  VehicleRepositoryImpl({
    required this._networkInfo,
    @Named('supabaseClient') supabase.SupabaseClient? supabaseClient,
  }) : _supabaseClient = supabaseClient ?? supabase.Supabase.instance.client;

  @override
  void dispose() {
    _vehiclesController.close();
  }

  @override
  Stream<List<VehicleModel>> get vehiclesStream => _vehiclesController.stream;

  /// Common method to execute a query with error handling
  Future<List<Map<String, dynamic>>> _executeQuery(
    String table, {
    Map<String, dynamic>? query,
    String? errorMessage,
  }) async {
    if (!await _networkInfo.isConnected) {
      throw const NetworkException('No internet connection');
    }

    try {
      var request = _supabaseClient
          .from(table)
          .select(query?['select'])
          .eq('is_available', true);
      
      // Apply ordering if specified
      if (query?['order'] != null) {
        request = request.order(query!['order'], ascending: false);
      } else {
        request = request.order('created_at', ascending: false);
      }
      
      // Apply filters
      if (query?['type'] != null) {
        final typeValue = (query!['type'] as String).split('.');
        if (typeValue[0] == 'eq') {
          request = request.eq('type', typeValue[1]);
        }
      }
      
      if (query?['daily_rate'] != null) {
        final dailyRateValue = (query!['daily_rate'] as String).split('.');
        if (dailyRateValue[0] == 'lte') {
          request = request.lte('daily_rate', double.tryParse(dailyRateValue[1]));
        }
      }
      
      // Apply pagination
      if (query?['limit'] != null) {
        final limit = query!['limit'] as int;
        final offset = query['offset'] as int? ?? 0;
        request = request.range(offset, offset + limit - 1);
      }
      
      final response = await request;
      return List<Map<String, dynamic>>.from(response);
    } on supabase.PostgrestException catch (e) {
      throw DatabaseException(
        message: errorMessage ?? 'Database error: ${e.message}',
        originalError: e,
      );
    } on Exception catch (e) {
      throw ServerException(
        message: errorMessage ?? 'Failed to execute query: $e',
        originalError: e,
      );
    }
  }
  @override
  Future<List<VehicleModel>> getVehicles({
    double? maxDistance,
    Position? userLocation,
    VehicleType? type,
    double? maxDailyRate,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      // Build base query
      var query = _buildVehicleQuery();
      
      // Apply filters
      if (type != null) {
        query['type'] = 'eq.${type.toString().split('.').last.toLowerCase()}';
      }
      if (maxDailyRate != null) {
        query['daily_rate'] = 'lte.$maxDailyRate';
      }
      
      // Apply pagination
      _applyPagination(query, limit: limit, offset: offset);
      
      // Execute the query
      final response = await _executeQuery(
        'vehicles',
        query: query,
        errorMessage: 'Failed to fetch vehicles',
      );
      
      // Parse the response into VehicleModel objects
      final vehicles = response.map<VehicleModel>((item) {
        try {
          return VehicleModelExtension.fromSupabaseJson(item);
        } catch (e) {
          developer.log('Error parsing vehicle: $e', error: e, stackTrace: StackTrace.current);
          rethrow;
        }
      }).toList();

      // Apply distance filter if location is provided (client-side filtering)
      if (userLocation != null && maxDistance != null) {
        return vehicles.where((vehicle) {
          if (vehicle.location == null) return false;
          
          final distance = Geolocator.distanceBetween(
            userLocation.latitude,
            userLocation.longitude,
            vehicle.location!.latitude,
            vehicle.location!.longitude,
          ) / 1000; // Convert meters to km
          
          // Store the distance for potential sorting
          vehicle.distanceFromUser = distance;
          
          return distance <= maxDistance;
        }).toList();
      }
      
      return vehicles;
    } on DatabaseException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } on ServerException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<VehicleModel?> getVehicleById(String id) async {
    try {
      final response = await _executeQuery(
        _supabaseClient
            .from('vehicles')
            .select('''
              *,
              owner:profiles!owner_id(
                id,
                full_name,
                avatar_url,
                rating,
                created_at
              ),
              features:vehicle_features!vehicle_id(feature:features(name)),
              reviews:reviews!vehicle_id(
                id,
                rating,
                comment,
                created_at,
                user:profiles!reviews_user_id_fkey(
                  id,
                  full_name,
                  avatar_url
                )
              ),
              (SELECT AVG(rating)::numeric(10,1) FROM reviews WHERE vehicle_id = vehicles.id) as avg_rating,
              (SELECT COUNT(*) FROM bookings WHERE vehicle_id = vehicles.id) as booking_count
            ''')
            .eq('id', id)
            .single(),
        errorMessage: 'Failed to fetch vehicle details',
      );

      if (response.isEmpty) return null;
      
      // Parse the vehicle data
      final vehicle = VehicleModel.fromJson(response.first);
      
      // Update the vehicle stream
      if (!_vehiclesController.isClosed) {
        _vehiclesController.add([vehicle]);
      }
      
      return vehicle;
    } on DatabaseException catch (e) {
      if (e is NotFoundException) return null;
      throw SupabaseErrorHandler.handleError(e);
    } on ServerException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }
            0,
            (sum, review) => sum + (review['rating'] as num).toDouble(),
          );
          // Store the average rating (you might want to add this field to VehicleModel)
          // vehicle.avgRating = totalRating / reviews.length;
        }
      }
      
      return vehicle;
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        // Not found
        return null;
      }
      throw ServerFailure(
        message: 'Database error while fetching vehicle',
        code: e.code,
        details: e.details,
        hint: e.hint,
      );
    } catch (e) {
      throw ServerFailure(message: 'Failed to get vehicle: ${e.toString()}');
    }
  }

  @override
  Future<List<VehicleModel>> getVehiclesByOwner(String ownerId) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            )
          ''')
          .eq('owner_id', ownerId)
          .order('created_at', ascending: false);

      if (response == null) return [];
      
      return (response as List)
          .map((json) => VehicleModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerFailure(
        message: 'Database error while fetching owner vehicles',
        code: e.code,
        details: e.details,
        hint: e.hint,
      );
    } catch (e) {
      throw ServerFailure(message: 'Failed to get owner vehicles: ${e.toString()}');
    }
  }

  @override
  Future<List<VehicleModel>> getVehiclesAdvanced(VehicleFilterModel filter) async {
    try {
      // Use the standard query builder with simplified filters
      var query = _supabaseClient
          .from('vehicles')
          .select('*, profiles:owner_id(*)')
          .eq('is_available', true)
          .limit(filter.limit)
          .range(filter.offset, filter.offset + filter.limit - 1);

      // Apply filters
      if (filter.vehicleType != null) {
        query = query.eq('type', filter.vehicleType.toString().split('.').last.toLowerCase());
      }

      if (filter.minDailyRate != null) {
        query = query.gte('daily_rate', filter.minDailyRate);
      }

      if (filter.maxDailyRate != null) {
        query = query.lte('daily_rate', filter.maxDailyRate);
      }

      if (filter.make != null && filter.make!.isNotEmpty) {
        query = query.ilike('make', '%${filter.make}%');
      }

      if (filter.model != null && filter.model!.isNotEmpty) {
        query = query.ilike('model', '%${filter.model}%');
      }

      // Apply year filters
      if (filter.minYear != null) {
        query = query.gte('year', filter.minYear.toString());
      }
      if (filter.maxYear != null) {
        query = query.lte('year', filter.maxYear.toString());
      }

      // Apply distance filter if location is provided
      if (filter.userLocation != null && filter.maxDistance != null) {
        // For Supabase, we need to use PostGIS functions for distance filtering
        // This is a simplified approach - in production, you'd use the PostGIS extension
        query = query.neq('location', null); // Only get vehicles with location data
      }

      // Apply feature filters
      if (filter.requiredFeatures != null && filter.requiredFeatures!.isNotEmpty) {
        // For Supabase, we need to check if the features column contains all required features
        // We'll do client-side filtering for features in the result processing below
      }

      // Apply text search if provided
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        // For Supabase, we'll use a simpler approach with multiple LIKE queries
        String searchTerm = '%${filter.searchQuery!}%';
        query = query.or('make.like.$searchTerm,model.like.$searchTerm,description.like.$searchTerm');
      }

      // Apply sorting
      if (filter.sortOrder != null) {
        switch (filter.sortOrder) {
          case VehicleSortOrder.newest:
            query = query.order('created_at', ascending: false);
            break;
          case VehicleSortOrder.oldest:
            query = query.order('created_at', ascending: true);
            break;
          case VehicleSortOrder.priceAsc:
            query = query.order('daily_rate', ascending: true);
            break;
          case VehicleSortOrder.priceDesc:
            query = query.order('daily_rate', ascending: false);
            break;
          case VehicleSortOrder.highestRated:
            query = query.order('avg_rating', ascending: false);
            break;
          case VehicleSortOrder.mostPopular:
            query = query.order('booking_count', ascending: false);
            break;
          case VehicleSortOrder.nearest:
            // Nearest will be handled client-side
            break;
        }
      } else {
        // Default sort by newest
        query = query.order('created_at', ascending: false);
      }

      final response = await query;

      List<VehicleModel> vehicles = (response as List)
          .map((json) => VehicleModel.fromJson(json))
          .toList();

      // Apply client-side filters that couldn't be done with Supabase queries
      vehicles = vehicles.where((vehicle) {
        // Vehicle type filter
        if (filter.vehicleType != null &&
            vehicle.type.toString().split('.').last.toLowerCase() !=
                filter.vehicleType.toString().split('.').last.toLowerCase()) {
          return false;
        }

        // Price filters
        if (filter.minDailyRate != null && vehicle.dailyRate < filter.minDailyRate!) {
          return false;
        }
        if (filter.maxDailyRate != null && vehicle.dailyRate > filter.maxDailyRate!) {
          return false;
        }
        if (filter.minHourlyRate != null && vehicle.hourlyRate < filter.minHourlyRate!) {
          return false;
        }
        if (filter.maxHourlyRate != null && vehicle.hourlyRate > filter.maxHourlyRate!) {
          return false;
        }

        // Make and model filters
        if (filter.make != null && filter.make!.isNotEmpty &&
            !vehicle.make.toLowerCase().contains(filter.make!.toLowerCase())) {
          return false;
        }
        if (filter.model != null && filter.model!.isNotEmpty &&
            !vehicle.model.toLowerCase().contains(filter.model!.toLowerCase())) {
          return false;
        }

        // Year filters
        if (filter.minYear != null && vehicle.year < filter.minYear!) {
          return false;
        }
        if (filter.maxYear != null && vehicle.year > filter.maxYear!) {
          return false;
        }

        // Text search filter
        if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
          final query = filter.searchQuery!.toLowerCase();
          final makeMatch = vehicle.make.toLowerCase().contains(query);
          final modelMatch = vehicle.model.toLowerCase().contains(query);
          final descMatch = vehicle.description?.toLowerCase().contains(query) ?? false;

          if (!makeMatch && !modelMatch && !descMatch) {
            return false;
          }
        }

        // Feature filters
        if (filter.requiredFeatures != null && filter.requiredFeatures!.isNotEmpty) {
          if (vehicle.features == null) return false;

          for (final feature in filter.requiredFeatures!) {
            if (!vehicle.features!.contains(feature)) {
              return false;
            }
          }
        }

        // Distance filter
        if (filter.userLocation != null && filter.maxDistance != null) {
          if (vehicle.location == null) return false;

          final distance = Geolocator.distanceBetween(
            filter.userLocation!.latitude,
            filter.userLocation!.longitude,
            vehicle.location.latitude,
            vehicle.location.longitude,
          ) / 1000; // Convert meters to km

          // Store the distance for sorting later
          vehicle.distanceFromUser = distance;

          if (distance > filter.maxDistance!) {
            return false;
          }
        }

        // Date availability filtering
        if (filter.availableFrom != null || filter.availableUntil != null) {
          // In a real implementation, we would check against bookings
          // For now, just check if the vehicle is generally available
          if (!vehicle.isAvailable) {
            return false;
          }
        }

        return true;
      }).toList();

      // Apply sorting
      if (filter.sortOrder != null) {
        switch (filter.sortOrder!) {
          case VehicleSortOrder.newest:
            vehicles.sort((a, b) => b.createdAt.compareTo(a.createdAt));
            break;
          case VehicleSortOrder.oldest:
            vehicles.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            break;
          case VehicleSortOrder.priceAsc:
            vehicles.sort((a, b) => a.dailyRate.compareTo(b.dailyRate));
            break;
          case VehicleSortOrder.priceDesc:
            vehicles.sort((a, b) => b.dailyRate.compareTo(a.dailyRate));
            break;
          case VehicleSortOrder.highestRated:
            vehicles.sort((a, b) => (b.avgRating ?? 0).compareTo(a.avgRating ?? 0));
            break;
          case VehicleSortOrder.mostPopular:
            vehicles.sort((a, b) => (b.bookingCount ?? 0).compareTo(a.bookingCount ?? 0));
            break;
          case VehicleSortOrder.nearest:
            if (filter.userLocation != null) {
              vehicles.sort((a, b) =>
                  (a.distanceFromUser ?? double.infinity)
                      .compareTo(b.distanceFromUser ?? double.infinity));
            }
            break;
        }
      } else {
        // Default sort by newest
        vehicles.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }

      return vehicles;
    } catch (e) {
      throw ServerFailure(message: 'Failed to get vehicles with advanced filtering: $e');
    }
  }

  @override
  Future<List<String>> getAvailableVehicleMakes() async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('make')
          .eq('is_available', true)
          .order('make');

      // Extract unique makes
      Set<String> uniqueMakes = {};
      for (var item in response) {
        uniqueMakes.add(item['make'] as String);
      }
      
      return uniqueMakes.toList();
    } catch (e) {
      throw ServerFailure(message: 'Failed to get vehicle makes: $e');
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleModels(String make) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('model')
          .eq('is_available', true)
          .eq('make', make)
          .order('model');
      
      // Extract unique models
      Set<String> uniqueModels = {};
      for (var item in response) {
        uniqueModels.add(item['model'] as String);
      }
      
      return uniqueModels.toList();
    } catch (e) {
      throw ServerFailure(message: 'Failed to get vehicle models: $e');
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleFeatures() async {
    try {
      final response = await _supabaseClient
          .from('vehicle_features')
          .select('name')
          .order('name');
      
      return (response as List).map((item) => item['name'] as String).toList();
    } catch (e) {
      throw ServerFailure(message: 'Failed to get vehicle features: $e');
    }
  Future<VehicleModel> createVehicle(VehicleModel vehicle) async {
    try {
      // Convert the vehicle to JSON and remove any null values
      final vehicleJson = vehicle.toJson()..removeWhere((key, value) => value == null);
      
      final response = await _supabaseClient
          .from('vehicles')
          .insert(vehicleJson)
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            )
          ''')
          .single();

      return VehicleModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerFailure(
        message: 'Database error while creating vehicle',
        code: e.code,
        details: e.details,
        hint: e.hint,
      );
    } catch (e) {
      throw ServerFailure(message: 'Failed to create vehicle: ${e.toString()}');
    }
  }
  
  @override
  Future<VehicleModel> updateVehicle(VehicleModel vehicle) async {
    try {
      // Convert the vehicle to JSON and remove any null values
      final vehicleJson = vehicle.toJson()
        ..removeWhere((key, value) => value == null)
        ..remove('id') // Don't update the ID
        ..remove('owner_id') // Don't update the owner
        ..remove('created_at'); // Don't update the creation date
      
      final response = await _supabaseClient
          .from('vehicles')
          .update(vehicleJson)
          .eq('id', vehicle.id)
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            )
          ''')
          .single();

      return VehicleModel.fromJson(response);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw NotFoundFailure('Vehicle not found');
      }
      throw ServerFailure(
        message: 'Database error while updating vehicle',
        code: e.code,
        details: e.details,
        hint: e.hint,
      );
    } catch (e) {
      throw ServerFailure(message: 'Failed to update vehicle: ${e.toString()}');
    }
  }
  
  @override
  Future<void> deleteVehicle(String id) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .delete()
          .eq('id', id);
          
      if (response == null || (response is List && response.isEmpty)) {
        throw NotFoundFailure('Vehicle not found');
      }
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw NotFoundFailure('Vehicle not found');
      }
      throw ServerFailure(
        message: 'Database error while deleting vehicle',
        code: e.code,
        details: e.details,
        hint: e.hint,
      );
    } catch (e) {
      throw ServerFailure(message: 'Failed to delete vehicle: ${e.toString()}');
    }
  }
  
  @override
  Future<bool> toggleAvailability(String id, bool isAvailable) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .update({'is_available': isAvailable, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', id)
          .select('is_available')
          .single();
          
      if (response == null) {
        throw NotFoundFailure('Vehicle not found');
      }
      
      return response['is_available'] as bool;
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        throw NotFoundFailure('Vehicle not found');
      }
      throw ServerFailure(
        message: 'Database error while toggling vehicle availability',
        code: e.code,
        details: e.details,
        hint: e.hint,
      );
    } catch (e) {
      throw ServerFailure(message: 'Failed to toggle vehicle availability: ${e.toString()}');
    }
  }
  
  @override
  Future<List<VehicleModel>> searchVehicles(String query, {int limit = 20, int offset = 0}) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('*, profiles:owner_id(*)')
          .textSearch('fts', query, config: 'english')
          .order('created_at', ascending: false)
          .limit(limit)
          .range(offset, offset + limit - 1);
      
      return (response as List)
          .map((json) => VehicleModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to search vehicles: $e');
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleMakes() async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('make')
          .order('make');
          
      if (response == null) return [];
      
      // Extract unique makes and sort them
      final makes = (response as List)
          .map((e) => e['make'] as String?)
          .whereType<String>()
          .toSet()
          .toList()
        ..sort();
        
      return makes;
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleModels(String make) async {
    if (make.isEmpty) return [];
    
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('model')
          .eq('make', make)
          .order('model');
          
      if (response == null) return [];
      
      // Extract unique models and sort them
      final models = (response as List)
          .map((e) => e['model'] as String?)
          .whereType<String>()
          .toSet()
          .toList()
        ..sort();
        
      return models;
    } on PostgrestException catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }
  
  @override
  Future<List<String>> getAvailableVehicleFeatures() async {
    try {
      // First try to get features from a dedicated features table if it exists
      try {
        final response = await _supabaseClient
            .from('vehicle_features')
            .select('name')
            .order('name', ascending: true);
            
        if (response != null && response is List && response.isNotEmpty) {
          return (response as List)
              .where((e) => e['name'] != null && e['name'].toString().isNotEmpty)
              .map((e) => e['name'].toString().trim())
              .toSet()
              .toList()
              ..sort((a, b) => a.compareTo(b));
        }
      } catch (_) {
        // If the features table doesn't exist or there's an error, fall back to the static list
      }
      
      // Fallback to a static list of common features
      return [
        'Air Conditioning',
        'Bluetooth',
        'GPS',
        'USB Port',
        'Sunroof',
        'Leather Seats',
        'Heated Seats',
        'Backup Camera',
        'Blind Spot Monitor',
        'Parking Sensors',
        'Keyless Entry',
        'Push Button Start',
        'Apple CarPlay',
        'Android Auto',
        'WiFi Hotspot',
        'Bike Rack',
        'Roof Rack',
        'Pet Friendly',
        'Wheelchair Accessible',
      ];
    } catch (e) {
      // If everything fails, log the error but still return the default list
      print('Error fetching vehicle features: $e');
      return [];
    }
  }
  
  @override
  Future<VehicleModel> getVehicleById(String id) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url,
              rating,
              created_at
            ),
            reviews:reviews!reviews_vehicle_id_fkey(
              id,
              rating,
              comment,
              created_at,
              reviewer:profiles!reviews_reviewer_id_fkey(
                id,
                full_name,
                avatar_url
              )
            )
          ''')
          .eq('id', id)
          .single();

      return VehicleModel.fromJson(response);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') { // Not found
        throw NotFoundFailure('Vehicle not found');
      }
      throw SupabaseErrorHandler.handleError(e);
    } catch (e) {
      throw SupabaseErrorHandler.handleError(e);
    }
  }

  @override
  Future<List<VehicleModel>> getVehiclesByOwner(String ownerId) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url
            )
          ''')
          .eq('owner_id', ownerId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => VehicleModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw ServerFailure(
        message: 'Failed to fetch owner vehicles',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      throw ServerFailure(
        message: 'Unexpected error while fetching owner vehicles',
        details: e.toString(),
      );
    }
  }

  @override
  Future<VehicleModel> createVehicle(VehicleModel vehicle) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .insert(vehicle.toJson()..remove('id'))
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url
            )
          ''')
          .single();

      return VehicleModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw ServerFailure(
        message: 'Failed to create vehicle',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      throw ServerFailure(
        message: 'Unexpected error while creating vehicle',
        details: e.toString(),
      );
    }
  }

  @override
  Future<VehicleModel> updateVehicle(VehicleModel vehicle) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .update(vehicle.toJson())
          .eq('id', vehicle.id)
          .select('''
            *,
            profiles:owner_id(
              id,
              full_name,
              avatar_url
            )
          ''')
          .single();

      return VehicleModel.fromJson(response);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') { // Not found
        throw NotFoundFailure('Vehicle not found');
      }
      throw ServerFailure(
        message: 'Failed to update vehicle',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      throw ServerFailure(
        message: 'Unexpected error while updating vehicle',
        details: e.toString(),
      );
    }
  }

  @override
  Future<void> deleteVehicle(String id) async {
    try {
      final response = await _supabaseClient
          .from('vehicles')
          .delete()
          .eq('id', id)
          .execute();

      if (response.error != null) {
        throw SupabaseErrorHandler.handleError(response.error!);
      }

      if (response.data == null || (response.data is List && (response.data as List).isEmpty)) {
        throw NotFoundFailure('Vehicle not found');
      }
    } on PostgrestException catch (e) {
      throw ServerFailure(
        message: 'Failed to delete vehicle',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      throw ServerFailure(
        message: 'Unexpected error while deleting vehicle',
        details: e.toString(),
      );
    }
  }

  @override
  Future<bool> toggleAvailability(String id, bool isAvailable) async {
    try {
      await _supabaseClient
          .from('vehicles')
          .update({'is_available': isAvailable, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', id);
      return isAvailable;
    } on PostgrestException catch (e) {
      throw ServerFailure(
        message: 'Failed to toggle vehicle availability',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      throw ServerFailure(
        message: 'Unexpected error while toggling vehicle availability',
        details: e.toString(),
      );
    }
  }
}
