import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_filter_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';
import 'package:gaadi_sewa/features/vehicles/data/repositories/vehicle_repository_impl.dart';
import 'package:gaadi_sewa/core/providers/supabase_provider.dart';

// Provider for VehicleRepository
final vehicleRepositoryProvider = Provider<VehicleRepository>((ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return VehicleRepositoryImpl(supabase);
});

// Provider for fetching vehicles with basic filters
final vehiclesProvider = FutureProvider.family<List<VehicleModel>, Map<String, dynamic>>((ref, filters) async {
  final repository = ref.watch(vehicleRepositoryProvider);
  return repository.getVehicles(filters);
});

// Provider for fetching vehicles with advanced filters
final vehiclesAdvancedProvider = FutureProvider.family<List<VehicleModel>, VehicleFilterModel>((ref, filter) async {
  final repository = ref.watch(vehicleRepositoryProvider);
  return repository.getVehiclesAdvanced(filter);
});

// Provider for searching vehicles by query
final vehicleSearchProvider = FutureProvider.family<List<VehicleModel>, String>((ref, query) async {
  final repository = ref.watch(vehicleRepositoryProvider);
  final filter = VehicleFilterModel(searchQuery: query);
  return repository.getVehiclesAdvanced(filter);
});

// Provider for fetching a single vehicle by ID
final vehicleByIdProvider = FutureProvider.family<VehicleModel?, String>((ref, id) async {
  final repository = ref.watch(vehicleRepositoryProvider);
  return repository.getVehicleById(id);
});

// Provider for fetching all available vehicle makes
final vehicleMakesProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.watch(vehicleRepositoryProvider);
  return repository.getVehicleMakes();
});

// Provider for fetching all available vehicle features
final vehicleFeaturesProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.watch(vehicleRepositoryProvider);
  return repository.getVehicleFeatures();
});

// State provider for managing the current filter
class VehicleFilterNotifier extends StateNotifier<VehicleFilterModel> {
  VehicleFilterNotifier() : super(VehicleFilterModel());
  
  void updateFilter(VehicleFilterModel filter) {
    state = filter;
  }
  
  void resetFilter() {
    state = const VehicleFilterModel();
  }
  
  void setVehicleType(VehicleType? type) {
    state = state.copyWith(vehicleType: type, clearVehicleType: type == null);
  }
  
  void setPriceRange({double? minDaily, double? maxDaily, double? minHourly, double? maxHourly}) {
    state = state.copyWith(
      minDailyRate: minDaily,
      maxDailyRate: maxDaily,
      minHourlyRate: minHourly,
      maxHourlyRate: maxHourly,
      clearMinDailyRate: minDaily == null,
      clearMaxDailyRate: maxDaily == null,
      clearMinHourlyRate: minHourly == null,
      clearMaxHourlyRate: maxHourly == null,
    );
  }
  
  void setYearRange({int? minYear, int? maxYear}) {
    state = state.copyWith(
      minYear: minYear,
      maxYear: maxYear,
      clearMinYear: minYear == null,
      clearMaxYear: maxYear == null,
    );
  }
  
  void setMakeModel({String? make, String? model}) {
    state = state.copyWith(
      make: make,
      model: model,
      clearMake: make == null,
      clearModel: model == null,
    );
  }
  
  void setLocation({Position? location, double? maxDistance}) {
    state = state.copyWith(
      userLocation: location,
      maxDistance: maxDistance,
      clearUserLocation: location == null,
      clearMaxDistance: maxDistance == null,
    );
  }
  
  void setFeatures(List<String>? features) {
    state = state.copyWith(
      requiredFeatures: features,
      clearRequiredFeatures: features == null || features.isEmpty,
    );
  }
  
  void setSearchQuery(String? query) {
    state = state.copyWith(
      searchQuery: query,
      clearSearchQuery: query == null || query.isEmpty,
    );
  }
  
  void setSortOrder(VehicleSortOrder? sortOrder) {
    state = state.copyWith(
      sortOrder: sortOrder,
      clearSortOrder: sortOrder == null,
    );
  }
}

// Provider for the active filter
final vehicleFilterProvider = StateNotifierProvider<VehicleFilterNotifier, VehicleFilterModel>(
  (ref) => VehicleFilterNotifier(),
);

// Notifier for vehicle operations
class VehicleNotifier extends StateNotifier<AsyncValue<void>> {
  final VehicleRepository _repository;
  
  VehicleNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> createVehicle(VehicleModel vehicle) async {
    state = const AsyncValue.loading();
    try {
      await _repository.createVehicle(vehicle);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> updateVehicle(VehicleModel vehicle) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateVehicle(vehicle);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> deleteVehicle(String id) async {
    state = const AsyncValue.loading();
    try {
      await _repository.deleteVehicle(id);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<bool> toggleAvailability(String id, bool isAvailable) async {
    state = const AsyncValue.loading();
    try {
      final result = await _repository.toggleAvailability(id, isAvailable);
      state = const AsyncValue.data(null);
      return result;
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}

// Provider for VehicleNotifier
final vehicleNotifierProvider =
    StateNotifierProvider<VehicleNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(vehicleRepositoryProvider);
  return VehicleNotifier(repository);
});
