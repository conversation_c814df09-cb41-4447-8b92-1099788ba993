import 'package:geolocator_platform_interface/src/models/position.dart';
import 'package:json_annotation/json_annotation.dart';

part 'vehicle_model.g.dart';

/// A JsonConverter for Position objects
@JsonSerializable()
class PositionConverter implements JsonConverter<Position, Map<String, dynamic>> {
  const PositionConverter();

  @override
  Position fromJson(Map<String, dynamic> json) {
    return Position(
      longitude: json['longitude'] as double,
      latitude: json['latitude'] as double,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : DateTime.now(),
      accuracy: json['accuracy'] as double? ?? 0.0,
      altitude: json['altitude'] as double? ?? 0.0,
      heading: json['heading'] as double? ?? 0.0,
      speed: json['speed'] as double? ?? 0.0,
      speedAccuracy: json['speedAccuracy'] as double? ?? 0.0,
      altitudeAccuracy: json['altitudeAccuracy'] as double? ?? 0.0,
      headingAccuracy: json['headingAccuracy'] as double? ?? 0.0,
    );
  }

  @override
  Map<String, dynamic> toJson(Position position) {
    return {
      'longitude': position.longitude,
      'latitude': position.latitude,
      'timestamp': position.timestamp.toIso8601String(),
      'accuracy': position.accuracy,
      'altitude': position.altitude,
      'heading': position.heading,
      'speed': position.speed,
      'speedAccuracy': position.speedAccuracy,
      'altitudeAccuracy': position.altitudeAccuracy,
      'headingAccuracy': position.headingAccuracy,
    };
  }
}

enum VehicleType { bicycle, scooter, car }

@JsonSerializable()
class VehicleModel {
  final String id;
  final String ownerId;
  final VehicleType type;
  final String make;
  final String model;
  final int year;
  final double hourlyRate;
  final double dailyRate;
  @PositionConverter()
  final Position location;
  final bool isAvailable;
  final String? description;
  final List<String>? features;
  final List<String>? images;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  /// Distance from user's location in kilometers (not stored in database)
  /// Used for distance-based filtering and sorting
  @JsonKey(ignore: true)
  double? distanceFromUser;

  VehicleModel({
    required this.id,
    required this.ownerId,
    required this.type,
    required this.make,
    required this.model,
    required this.year,
    required this.hourlyRate,
    required this.dailyRate,
    required this.location,
    this.isAvailable = true,
    this.description,
    this.features,
    this.images,
    required this.createdAt,
    required this.updatedAt,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) => 
      _$VehicleModelFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleModelToJson(this);

  VehicleModel copyWith({
    String? id,
    String? ownerId,
    VehicleType? type,
    String? make,
    String? model,
    int? year,
    double? hourlyRate,
    double? dailyRate,
    Position? location,
    bool? isAvailable,
    String? description,
    List<String>? features,
    List<String>? images,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VehicleModel(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      type: type ?? this.type,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      dailyRate: dailyRate ?? this.dailyRate,
      location: location ?? this.location,
      isAvailable: isAvailable ?? this.isAvailable,
      description: description ?? this.description,
      features: features ?? this.features,
      images: images ?? this.images,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
