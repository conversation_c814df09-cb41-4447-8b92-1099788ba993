// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PositionConverter _$PositionConverterFromJson(Map<String, dynamic> json) =>
    PositionConverter();

Map<String, dynamic> _$PositionConverterToJson(PositionConverter instance) =>
    <String, dynamic>{};

VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) => VehicleModel(
      id: json['id'] as String,
      ownerId: json['ownerId'] as String,
      type: $enumDecode(_$VehicleTypeEnumMap, json['type']),
      make: json['make'] as String,
      model: json['model'] as String,
      year: (json['year'] as num).toInt(),
      hourlyRate: (json['hourlyRate'] as num).toDouble(),
      dailyRate: (json['dailyRate'] as num).toDouble(),
      location: const PositionConverter()
          .fromJson(json['location'] as Map<String, dynamic>),
      isAvailable: json['isAvailable'] as bool? ?? true,
      description: json['description'] as String?,
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$VehicleModelToJson(VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'ownerId': instance.ownerId,
      'type': _$VehicleTypeEnumMap[instance.type]!,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'hourlyRate': instance.hourlyRate,
      'dailyRate': instance.dailyRate,
      'location': const PositionConverter().toJson(instance.location),
      'isAvailable': instance.isAvailable,
      'description': instance.description,
      'features': instance.features,
      'images': instance.images,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$VehicleTypeEnumMap = {
  VehicleType.bicycle: 'bicycle',
  VehicleType.scooter: 'scooter',
  VehicleType.car: 'car',
};
