import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:gaadi_sewa/core/constants/api_endpoints.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/payments/data/models/payment_model.dart';
import 'package:gaadi_sewa/features/payments/domain/repositories/payment_repository.dart';
import 'package:http/http.dart' as http;

class PaymentRepositoryImpl implements PaymentRepository {
  final NetworkInfo networkInfo;
  final http.Client client;

  PaymentRepositoryImpl({
    required this.networkInfo,
    required this.client,
  });

  @override
  Future<Either<Failure, PaymentModel>> processPayment({
    required String bookingId,
    required double amount,
    required String paymentMethod,
    Map<String, dynamic>? metadata,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await client.post(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.payments}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'bookingId': bookingId,
          'amount': amount,
          'paymentMethod': paymentMethod,
          'metadata': metadata,
        }),
      );

      if (response.statusCode == 201) {
        final payment = PaymentModel.fromJson(jsonDecode(response.body));
        return Right(payment);
      } else {
        return Left(ServerFailure(
          message: 'Failed to process payment',
          statusCode: response.statusCode,
          error: response.body,
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, PaymentModel>> getPayment(String paymentId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await client.get(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.payments}/$paymentId'),
      );

      if (response.statusCode == 200) {
        final payment = PaymentModel.fromJson(jsonDecode(response.body));
        return Right(payment);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure());
      } else {
        return Left(ServerFailure(
          message: 'Failed to get payment',
          statusCode: response.statusCode,
          error: response.body,
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
        error: e.error,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: 'Unexpected error: $e',
        statusCode: 500,
        error: '$e',
      ));
    }
  }

  @override
  Future<Either<Failure, List<PaymentModel>>> getPaymentsByBooking(
      String bookingId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await client.get(
        Uri.parse(
            '${ApiEndpoints.baseUrl}${ApiEndpoints.bookings}/$bookingId/payments'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        final payments = jsonList
            .map((json) => PaymentModel.fromJson(json as Map<String, dynamic>))
            .toList();
        return Right(payments);
      } else {
        return Left(ServerFailure(
          message: 'Failed to get payment history',
          statusCode: response.statusCode,
          error: response.body,
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
        error: e.error,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: 'Unexpected error: $e',
        statusCode: 500,
        error: '$e',
      ));
    }
  }

  @override
  Future<Either<Failure, PaymentModel>> updatePaymentStatus({
    required String paymentId,
    required String status,
    String? transactionId,
    Map<String, dynamic>? metadata,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await client.patch(
        Uri.parse('${ApiEndpoints.baseUrl}${ApiEndpoints.payments}/$paymentId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'status': status,
          if (transactionId != null) 'transactionId': transactionId,
          if (metadata != null) 'metadata': metadata,
        }),
      );

      if (response.statusCode == 200) {
        final payment = PaymentModel.fromJson(jsonDecode(response.body));
        return Right(payment);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure());
      } else {
        return Left(ServerFailure(
          message: 'Failed to check payment status',
          statusCode: response.statusCode,
          error: response.body,
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
        error: e.error,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: 'Unexpected error: $e',
        statusCode: 500,
        error: '$e',
      ));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyPayment({
    required String paymentId,
    required String transactionId,
  }) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await client.post(
        Uri.parse(
            '${ApiEndpoints.baseUrl}${ApiEndpoints.payments}/$paymentId/verify'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'transactionId': transactionId,
        }),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body) as Map<String, dynamic>;
        return Right(result['isValid'] as bool);
      } else if (response.statusCode == 404) {
        return Left(NotFoundFailure());
      } else {
        return Left(ServerFailure(
          message: 'Failed to verify payment',
          statusCode: response.statusCode,
          error: response.body,
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error: $e'));
    }
  }
}
