import 'package:dartz/dartz.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/error/failures.dart';
import 'package:gaadi_sewa/core/network/network_info.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/domain/repositories/booking_repository.dart';
import 'package:gaadi_sewa/features/vehicles/domain/models/vehicle_model.dart';
import 'package:gaadi_sewa/features/vehicles/domain/repositories/vehicle_repository.dart';

class BookingRepositoryImpl implements BookingRepository {
  final supabase.SupabaseClient supabaseClient;
  final VehicleRepository vehicleRepository;
  final NetworkInfo networkInfo;

  BookingRepositoryImpl({
    required this.supabaseClient,
    required this.vehicleRepository,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, BookingModel>> createBooking(BookingModel booking) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      // Check vehicle availability first
      final availability = await checkAvailability(
        booking.vehicleId,
        booking.startDate,
        booking.endDate,
      );

      return availability.fold(
        (failure) => Left(failure),
        (isAvailable) async {
          if (!isAvailable) {
            return Left(BookingFailure('Vehicle is not available for the selected dates'));
          }

          // Create booking in database
          final bookingData = booking.toJson()
            ..remove('vehicle') // Remove nested object before saving
            ..remove('id'); // Let Supabase generate the ID

          final response = await supabaseClient
              .from('bookings')
              .insert(bookingData)
              .select()
              .single();

          // Get the created booking with vehicle details
          return await getBooking(response['id']);
        },
      );
    } on supabase.PostgrestException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, BookingModel>> getBooking(String id) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await supabaseClient
          .from('bookings')
          .select()
          .eq('id', id)
          .single();

      // Get vehicle details
      final vehicleResult = await vehicleRepository.getVehicle(response['vehicle_id']);
      
      return vehicleResult.fold(
        (failure) => Left(failure),
        (vehicle) => Right(BookingModel.fromJson(response, vehicle)),
      );
    } on supabase.PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure('Booking not found'));
      }
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<BookingModel>>> getUserBookings(String userId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final response = await supabaseClient
          .from('bookings')
          .select()
          .eq('user_id', userId)
          .order('start_date', ascending: false);

      final bookings = <BookingModel>[];
      
      for (var bookingData in response) {
        final vehicleResult = await vehicleRepository.getVehicle(bookingData['vehicle_id']);
        await vehicleResult.fold(
          (failure) => null,
          (vehicle) => bookings.add(BookingModel.fromJson(bookingData, vehicle)),
        );
      }
      
      return Right(bookings);
    } on supabase.PostgrestException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<BookingModel>>> getOwnerBookings(String ownerId) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      // First get all vehicles owned by the user
      final vehiclesResult = await vehicleRepository.getVehiclesByOwner(ownerId);
      
      return vehiclesResult.fold(
        (failure) => Left(failure),
        (vehicles) async {
          if (vehicles.isEmpty) {
            return const Right([]);
          }
          
          final vehicleIds = vehicles.map((v) => v.id).toList();
          
          // Get all bookings for these vehicles
          final response = await supabaseClient
              .from('bookings')
              .select()
              .inFilter('vehicle_id', vehicleIds)
              .order('start_date', ascending: false);
          
          final bookings = <BookingModel>[];
          final vehicleMap = {for (var v in vehicles) v.id: v};
          
          for (var bookingData in response) {
            final vehicle = vehicleMap[bookingData['vehicle_id']];
            if (vehicle != null) {
              bookings.add(BookingModel.fromJson(bookingData, vehicle));
            }
          }
          
          return Right(bookings);
        },
      );
    } on supabase.PostgrestException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateBookingStatus(
    String bookingId, 
    String status, 
    {String? rejectionReason}
  ) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      final updateData = {
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
        if (rejectionReason != null) 'rejection_reason': rejectionReason,
      };

      await supabaseClient
          .from('bookings')
          .update(updateData)
          .eq('id', bookingId);

      return const Right(null);
    } on supabase.PostgrestException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> cancelBooking(String bookingId, String reason) async {
    return updateBookingStatus(
      bookingId, 
      BookingStatus.cancelled.toString().split('.').last,
      rejectionReason: reason,
    );
  }

  @override
  Future<Either<Failure, bool>> checkAvailability(
    String vehicleId, 
    DateTime startDate, 
    DateTime endDate, 
    {String? excludeBookingId}
  ) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      // Build the query to check for overlapping bookings
      var query = supabaseClient
          .from('bookings')
          .select('id')
          .eq('vehicle_id', vehicleId)
          .or('(start_date.lte.$endDate, and(end_date.gte.$startDate))')
          .filter('status', 'in', [
            BookingStatus.pending.toString().split('.').last,
            BookingStatus.confirmed.toString().split('.').last,
          ]);

      if (excludeBookingId != null) {
        query = query.neq('id', excludeBookingId);
      }

      final response = await query;
      
      // If no overlapping bookings, vehicle is available
      return Right(response.isEmpty);
    } on supabase.PostgrestException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, double>> calculatePrice(
    String vehicleId, 
    DateTime startDate, 
    DateTime endDate
  ) async {
    if (!await networkInfo.isConnected) {
      return Left(NetworkFailure());
    }

    try {
      // Get vehicle details
      final vehicleResult = await vehicleRepository.getVehicle(vehicleId);
      
      return vehicleResult.fold(
        (failure) => Left(failure),
        (vehicle) {
          // Calculate number of days (minimum 1 day)
          final days = endDate.difference(startDate).inDays + 1;
          
          // Calculate total price (simple calculation, can be enhanced with dynamic pricing)
          final basePrice = vehicle.dailyRate * days;
          
          // Apply any discounts or additional charges here if needed
          final totalPrice = basePrice;
          
          return Right(totalPrice);
        },
      );
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

class BookingFailure extends Failure {
  final String message;
  
  BookingFailure(this.message);
  
  @override
  List<Object?> get props => [message];
}
