import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/constants/colors.dart';
import 'package:gaadi_sewa/features/auth/presentation/providers/auth_provider.dart';
import 'package:gaadi_sewa/features/bookings/domain/models/booking_model.dart';
import 'package:gaadi_sewa/features/bookings/presentation/providers/booking_provider.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_card.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_filter_chip.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/booking_status_chip.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/empty_bookings.dart';
import 'package:gaadi_sewa/features/bookings/presentation/widgets/loading_bookings.dart';

class BookingListScreen extends ConsumerStatefulWidget {
  final bool isOwnerView;
  
  const BookingListScreen({
    Key? key,
    this.isOwnerView = false,
  }) : super(key: key);

  @override
  ConsumerState<BookingListScreen> createState() => _BookingListScreenState();
}

class _BookingListScreenState extends ConsumerState<BookingListScreen> {
  String _selectedFilter = 'all';
  final List<Map<String, dynamic>> _filters = [
    {'value': 'all', 'label': 'All'},
    {'value': 'upcoming', 'label': 'Upcoming'},
    {'value': 'past', 'label': 'Past'},
    {'value': 'pending', 'label': 'Pending'},
    {'value': 'confirmed', 'label': 'Confirmed'},
    {'value': 'cancelled', 'label': 'Cancelled'},
  ];

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(authStateProvider);
    final userId = user?.id ?? '';
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isOwnerView ? 'My Rentals' : 'My Bookings',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Filter chips
          SizedBox(
            height: 60,
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              children: _filters.map((filter) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: BookingFilterChip(
                    label: filter['label'],
                    isSelected: _selectedFilter == filter['value'],
                    onSelected: () {
                      setState(() {
                        _selectedFilter = filter['value'];
                      });
                    },
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Divider
          const Divider(height: 1, thickness: 1),
          
          // Bookings list
          Expanded(
            child: _buildBookingsList(userId),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBookingsList(String userId) {
    return Consumer(
      builder: (context, ref, child) {
        final bookingsAsync = widget.isOwnerView
            ? ref.watch(ownerBookingsProvider(userId))
            : ref.watch(userBookingsProvider(userId));
            
        return bookingsAsync.when(
          data: (bookings) {
            // Filter bookings based on selected filter
            final filteredBookings = _filterBookings(bookings);
            
            if (filteredBookings.isEmpty) {
              return EmptyBookings(
                isOwnerView: widget.isOwnerView,
                filter: _selectedFilter,
              );
            }
            
            return RefreshIndicator(
              onRefresh: () async {
                // Refresh the bookings
                ref.invalidate(bookingNotifierProvider);
                if (widget.isOwnerView) {
                  ref.invalidate(ownerBookingsProvider(userId));
                } else {
                  ref.invalidate(userBookingsProvider(userId));
                }
              },
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: filteredBookings.length,
                itemBuilder: (context, index) {
                  final booking = filteredBookings[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: BookingCard(
                      booking: booking,
                      isOwnerView: widget.isOwnerView,
                      onStatusUpdate: (status, {String? reason}) {
                        _updateBookingStatus(booking.id, status, reason: reason);
                      },
                    ),
                  );
                },
              ),
            );
          },
          loading: () => const LoadingBookings(),
          error: (error, stack) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load bookings',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (widget.isOwnerView) {
                        ref.invalidate(ownerBookingsProvider(userId));
                      } else {
                        ref.invalidate(userBookingsProvider(userId));
                      }
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
  
  List<BookingModel> _filterBookings(List<BookingModel> bookings) {
    final now = DateTime.now();
    
    return bookings.where((booking) {
      switch (_selectedFilter) {
        case 'upcoming':
          return booking.status.isActive && booking.startDate.isAfter(now);
        case 'past':
          return booking.endDate.isBefore(now);
        case 'pending':
          return booking.status == BookingStatus.pending;
        case 'confirmed':
          return booking.status == BookingStatus.confirmed;
        case 'cancelled':
          return booking.status == BookingStatus.cancelled;
        case 'all':
        default:
          return true;
      }
    }).toList();
  }
  
  Future<void> _updateBookingStatus(String bookingId, String status, {String? reason}) async {
    final notifier = ref.read(bookingNotifierProvider.notifier);
    
    try {
      await notifier.updateBookingStatus(
        bookingId, 
        status,
        rejectionReason: reason,
      );
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Booking ${status.toLowerCase()} successfully')),
        );
        
        // Refresh the bookings list
        ref.invalidate(bookingNotifierProvider);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update booking: $e')),
        );
      }
    }
  }
}
