import 'package:equatable/equatable.dart';

abstract class AppException with EquatableMix<PERSON> implements Exception {
  final String message;
  final String? code;
  final StackTrace? stackTrace;

  const AppException({
    required this.message,
    this.code,
    this.stackTrace,
  });

  @override
  List<Object?> get props => [message, code, stackTrace];

  @override
  bool? get stringify => true;
}

// Server exceptions
class ServerException extends AppException {
  const ServerException({
    required String message,
    String? code,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          code: code,
          stackTrace: stackTrace,
        );
}

// Cache exceptions
class CacheException extends AppException {
  const CacheException({
    required String message,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Network exceptions
class NetworkException extends AppException {
  const NetworkException({
    required String message,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class NoInternetException extends NetworkException {
  const NoInternetException({
    String message = 'No internet connection',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

class TimeoutException extends NetworkException {
  const TimeoutException({
    String message = 'Request timed out',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Authentication exceptions
class UnauthorizedException extends AppException {
  const UnauthorizedException({
    String message = 'Unauthorized access',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Validation exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? errors;

  const ValidationException({
    required String message,
    this.errors,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );

  @override
  List<Object?> get props => [message, errors, stackTrace];
}

// Not found exception
class NotFoundException extends AppException {
  const NotFoundException({
    String message = 'Resource not found',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Format exceptions
class FormatException extends AppException {
  const FormatException({
    required String message,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Permission exceptions
class PermissionDeniedException extends AppException {
  const PermissionDeniedException({
    String message = 'Permission denied',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Feature not available exception
class FeatureNotAvailableException extends AppException {
  const FeatureNotAvailableException({
    String message = 'This feature is not available',
    StackTrace? stackTrace,
  }) : super(
          message: message,
          stackTrace: stackTrace,
        );
}

// Database exception
class DatabaseException extends AppException {
  final dynamic originalError;

  const DatabaseException({
    required String message,
    this.originalError,
    String? code,
    StackTrace? stackTrace,
  }) : super(
          message: message,
          code: code,
          stackTrace: stackTrace,
        );
}
