import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gaadi_sewa/core/constants/app_constants.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';
import 'package:gaadi_sewa/core/utils/logger.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path_util;

/// A utility class for handling image-related operations
class ImageUtils {
  // Singleton pattern
  factory ImageUtils() => _instance;
  ImageUtils._internal();
  static final ImageUtils _instance = ImageUtils._internal();

  // Image picker instance
  final ImagePicker _imagePicker = ImagePicker();

  // Maximum file size for upload (5MB)
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB

  // Maximum image dimensions
  static const int maxWidth = 2000;
  static const int maxHeight = 2000;
  static const int thumbnailSize = 200;

  /// Picks an image from the gallery
  Future<File?> pickImageFromGallery({
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    bool requestFullMetadata = true,
  }) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
        imageQuality: imageQuality,
        requestFullMetadata: requestFullMetadata,
      );

      if (image == null) return null;

      return File(image.path);
    } on PlatformException catch (e) {
      Logger.e('Failed to pick image from gallery', error: e);
      if (e.code == 'photo_access_denied') {
        throw const PermissionDeniedException('Photo library access was denied');
      } else {
        rethrow;
      }
    } catch (e) {
      Logger.e('Failed to pick image from gallery', error: e);
      rethrow;
    }
  }

  /// Captures an image using the camera
  Future<File?> captureImage({
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    bool requestFullMetadata = true,
  }) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
        imageQuality: imageQuality,
        requestFullMetadata: requestFullMetadata,
      );

      if (image == null) return null;

      return File(image.path);
    } on PlatformException catch (e) {
      Logger.e('Failed to capture image', error: e);
      if (e.code == 'camera_access_denied') {
        throw const PermissionDeniedException('Camera access was denied');
      } else {
        rethrow;
      }
    } catch (e) {
      Logger.e('Failed to capture image', error: e);
      rethrow;
    }
  }

  /// Picks multiple images from the gallery
  Future<List<File>> pickMultipleImages({
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    int? maxImages,
    bool requestFullMetadata = true,
  }) async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
        imageQuality: imageQuality,
        requestFullMetadata: requestFullMetadata,
      );

      if (images.isEmpty) return [];

      // Limit the number of images if maxImages is specified
      final limitedImages = maxImages != null && maxImages > 0
          ? images.take(maxImages).toList()
          : images;

      return limitedImages.map((xFile) => File(xFile.path)).toList();
    } on PlatformException catch (e) {
      Logger.e('Failed to pick multiple images', error: e);
      if (e.code == 'photo_access_denied') {
        throw const PermissionDeniedException('Photo library access was denied');
      } else {
        rethrow;
      }
    } catch (e) {
      Logger.e('Failed to pick multiple images', error: e);
      rethrow;
    }
  }

  /// Validates an image file
  Future<void> validateImageFile(File file) async {
    try {
      // Check file size
      final length = await file.length();
      if (length > maxFileSize) {
        throw ValidationException(
          'Image size exceeds the maximum limit of 5MB',
        );
      }

      // Check image dimensions
      final decodedImage = await decodeImageFromList(file.readAsBytesSync());
      if (decodedImage.width > maxWidth || decodedImage.height > maxHeight) {
        throw ValidationException(
          'Image dimensions exceed the maximum limit of ${maxWidth}x$maxHeight',
        );
      }
    } on ValidationException {
      rethrow;
    } catch (e) {
      Logger.e('Failed to validate image file', error: e);
      throw const ValidationException('Invalid image file');
    }
  }

  /// Compresses an image file
  Future<File> compressImage(
    File file, {
    int? quality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);
      if (image == null) throw Exception('Failed to decode image');

      // Resize image if needed
      img.Image resizedImage = image;
      if (maxWidth != null || maxHeight != null) {
        resizedImage = img.copyResize(
          image,
          width: maxWidth,
          height: maxHeight,
          maintainAspect: true,
        );
      }

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final fileName = path_util.basename(file.path);
      final targetPath = '${tempDir.path}/compressed_$fileName';

      // Compress and save the image
      final compressedFile = File(targetPath);
      await compressedFile.writeAsBytes(
        img.encodeJpg(
          resizedImage,
          quality: quality ?? 85,
        ),
      );

      return compressedFile;
    } catch (e) {
      Logger.e('Failed to compress image', error: e);
      // Return original file if compression fails
      return file;
    }
  }

  /// Creates a thumbnail from an image file
  Future<File> createThumbnail(File file, {int size = thumbnailSize}) async {
    try {
      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);
      if (image == null) throw Exception('Failed to decode image');

      // Create a square thumbnail
      final thumbnail = img.copyResize(
        image,
        width: size,
        height: size,
        interpolation: img.Interpolation.average,
      );

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final fileName = path_util.basenameWithoutExtension(file.path);
      final targetPath = '${tempDir.path}/${fileName}_thumb.jpg';

      // Save the thumbnail
      final thumbnailFile = File(targetPath);
      await thumbnailFile.writeAsBytes(img.encodeJpg(thumbnail, quality: 80));

      return thumbnailFile;
    } catch (e) {
      Logger.e('Failed to create thumbnail', error: e);
      rethrow;
    }
  }

  /// Converts a file to a base64 string
  Future<String> fileToBase64(File file) async {
    try {
      final bytes = await file.readAsBytes();
      return base64Encode(bytes);
    } catch (e) {
      Logger.e('Failed to convert file to base64', error: e);
      rethrow;
    }
  }

  /// Converts a base64 string to a file
  Future<File> base64ToFile(String base64String, {String? fileName}) async {
    try {
      final bytes = base64Decode(base64String);
      final tempDir = await getTemporaryDirectory();
      final file = File(
        '${tempDir.path}/${fileName ?? 'image_${DateTime.now().millisecondsSinceEpoch}.jpg'}'
      );
      await file.writeAsBytes(bytes);
      return file;
    } catch (e) {
      Logger.e('Failed to convert base64 to file', error: e);
      rethrow;
    }
  }

  /// Gets an image provider from a file path, URL, or asset path
  ImageProvider getImageProvider(String? imagePath, {String? placeholderPath}) {
    if (imagePath == null || imagePath.isEmpty) {
      return placeholderPath != null
          ? AssetImage(placeholderPath)
          : const AssetImage('assets/images/placeholder.png');
    }

    if (imagePath.startsWith('http')) {
      return NetworkImage(imagePath);
    } else if (imagePath.startsWith('assets/')) {
      return AssetImage(imagePath);
    } else {
      return FileImage(File(imagePath));
    }
  }

  /// Gets a cached network image provider
  ImageProvider getCachedNetworkImage(
    String? imageUrl, {
    String? placeholderPath,
    Map<String, String>? headers,
  }) {
    if (imageUrl == null || imageUrl.isEmpty) {
      return placeholderPath != null
          ? AssetImage(placeholderPath)
          : const AssetImage('assets/images/placeholder.png');
    }

    // In a real app, you would use a package like `cached_network_image`
    // For now, we'll just return a NetworkImage
    return NetworkImage(
      imageUrl,
      headers: headers,
    );
  }

  /// Gets a file size string in a human-readable format
  String getFileSizeString(int bytes, {int decimals = 1}) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    final i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  /// Gets the MIME type of a file
  String getMimeType(String filePath) {
    final extension = path_util.extension(filePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.bmp':
        return 'image/bmp';
      case '.svg':
        return 'image/svg+xml';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case '.zip':
        return 'application/zip';
      case '.txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }

  /// Checks if a file is an image based on its extension
  bool isImageFile(String filePath) {
    final extension = path_util.extension(filePath).toLowerCase();
    return [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.bmp',
      '.svg',
    ].contains(extension);
  }

  /// Gets a placeholder image provider
  ImageProvider getPlaceholderImage() {
    return const AssetImage('assets/images/placeholder.png');
  }

  /// Gets a circular avatar image
  ImageProvider getAvatarImage(String? imageUrl, {String? name}) {
    if (imageUrl != null && imageUrl.isNotEmpty) {
      return getImageProvider(imageUrl);
    } else if (name != null && name.isNotEmpty) {
      // Generate a colorful avatar based on the name
      // This is a simplified version - in a real app, you might want to use a package like `avatar_glow`
      return getPlaceholderImage();
    } else {
      return getPlaceholderImage();
    }
  }

  /// Gets a file icon based on the file extension
  IconData getFileIcon(String filePath) {
    final extension = path_util.extension(filePath).toLowerCase();
    
    // Image files
    if (isImageFile(filePath)) return Icons.image;
    
    // Document files
    if (['.pdf'].contains(extension)) return Icons.picture_as_pdf;
    if (['.doc', '.docx'].contains(extension)) return Icons.description;
    if (['.xls', '.xlsx'].contains(extension)) return Icons.table_chart;
    if (['.ppt', '.pptx'].contains(extension)) return Icons.slideshow;
    if (['.txt', '.rtf', '.md'].contains(extension)) return Icons.text_fields;
    
    // Archive files
    if (['.zip', '.rar', '.7z', '.tar', '.gz'].contains(extension)) {
      return Icons.archive;
    }
    
    // Audio files
    if (['.mp3', '.wav', '.ogg', '.m4a', '.aac'].contains(extension)) {
      return Icons.audiotrack;
    }
    
    // Video files
    if (['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv'].contains(extension)) {
      return Icons.videocam;
    }
    
    // Default file icon
    return Icons.insert_drive_file;
  }
}

// Global instance of the image utils
final imageUtils = ImageUtils();
