import 'dart:async';
import 'dart:io' show InternetAddress, SocketException;

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gaadi_sewa/core/error/exceptions.dart';

abstract class NetworkInfo {
  Future<bool> get isConnected;
  Future<bool> get isWifiConnected;
  Future<bool> get isMobileDataConnected;
  Stream<ConnectivityResult> get onConnectivityChanged;
}

class NetworkInfoImpl implements NetworkInfo {
  final Connectivity connectivity;

  NetworkInfoImpl({required this.connectivity});

  @override
  Future<bool> get isConnected async {
    try {
      final connectivityResult = await connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }
      
      // Additional check to verify actual internet connectivity
      try {
        final result = await InternetAddress.lookup('google.com');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      } on SocketException catch (_) {
        return false;
      }
    } catch (e) {
      throw NetworkException(
        'Failed to check network connectivity: $e',
        StackTrace.current,
      );
    }
  }

  @override
  Future<bool> get isWifiConnected async {
    try {
      final connectivityResult = await connectivity.checkConnectivity();
      return connectivityResult == ConnectivityResult.wifi;
    } catch (e) {
      throw NetworkException(
        'Failed to check WiFi connectivity: $e',
        StackTrace.current,
      );
    }
  }

  @override
  Future<bool> get isMobileDataConnected async {
    try {
      final connectivityResult = await connectivity.checkConnectivity();
      return connectivityResult == ConnectivityResult.mobile;
    } catch (e) {
      throw NetworkException(
        'Failed to check mobile data connectivity: $e',
        StackTrace.current,
      );
    }
  }

  @override
  Stream<ConnectivityResult> get onConnectivityChanged => connectivity.onConnectivityChanged;
}

// Provider for Connectivity
final connectivityProvider = Provider<Connectivity>((ref) {
  return Connectivity();
});

// Provider for NetworkInfo
final networkInfoProvider = Provider<NetworkInfo>((ref) {
  final connectivity = ref.watch(connectivityProvider);
  return NetworkInfoImpl(connectivity: connectivity);
});
