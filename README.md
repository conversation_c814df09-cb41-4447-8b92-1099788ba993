# GaadiSewa+ 🚗

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Flutter](https://img.shields.io/badge/Flutter-02569B?style=flat&logo=flutter&logoColor=white)](https://flutter.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-181818?style=flat&logo=supabase&logoColor=white)](https://supabase.com/)

A peer-to-peer vehicle rental platform for urban Nepal, connecting vehicle owners with renters for flexible, affordable transportation. Now in production! 🚀

## ✨ Features

- **Vehicle Rentals** 🚗
  - Browse available vehicles (bicycles, scooters, cars)
  - Advanced search with filters
  - Real-time availability calendar
  - Location-based discovery

- **User Experience** 👤
  - Secure authentication
  - User profiles with verification
  - In-app messaging system
  - Booking management

- **Payments** 💳
  - Secure payment processing
  - Khalti payment gateway integration
  - Transparent pricing
  - Booking receipts

- **Reviews & Ratings** ⭐
  - Rate your experience
  - Read verified reviews
  - Helpful review voting
  - Review reporting system

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (3.19.0 or higher)
- Dart SDK (3.3.0 or higher)
- Android Studio / Xcode (for emulators)
- VS Code (recommended) with Flutter/Dart extensions
- Git
- Supabase account
- Khalti merchant account (for payments)

### 🛠 Quick Start

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/gaadi-sewa.git
   cd gaadi-sewa
   ```

2. **Run the setup script**:
   ```bash
   # Make the script executable (if needed)
   chmod +x setup.sh
   
   # Run the setup script
   ./setup.sh
   ```
   This will:
   - Check for required tools
   - Install dependencies
   - Set up environment variables
   - Configure Git hooks
   - Generate necessary files

3. **Update Environment Variables**:
   - Edit the `.env` file with your credentials:
     ```
     # Supabase
     SUPABASE_URL=your_supabase_url
     SUPABASE_ANON_KEY=your_supabase_anon_key
     
     # Khalti Payment Gateway
     KHALTI_PUBLIC_KEY=your_khalti_public_key
     
     # Mapbox (for maps and location services)
     MAPBOX_ACCESS_TOKEN=your_mapbox_token
     
     # App Configuration
     APP_ENV=development
     DEBUG=true
     ```

4. **Run the App**:
   ```bash
   # For web development
   flutter run -d chrome --web-renderer html --dart-define-from-file=.env
   
   # For Android
   flutter run -d <device_id> --dart-define-from-file=.env
   
   # For iOS (macOS only)
   flutter run -d <device_id> --dart-define-from-file=.env
   ```

5. **Run Tests**:
   ```bash
   # Run all tests
   flutter test
   
   # Run tests with coverage
   flutter test --coverage
   
   # Generate coverage report (requires lcov)
   genhtml coverage/lcov.info -o coverage/html
   open coverage/html/index.html
   ```

6. **Build for Production**:
   ```bash
   # Build Android APK
   flutter build apk --release --dart-define-from-file=.env
   
   # Build Android App Bundle
   flutter build appbundle --release --dart-define-from-file=.env
   
   # Build iOS (macOS only)
   flutter build ipa --export-options-plist=ios/exportOptions.plist --dart-define-from-file=.env
   ```

## 📚 Project Documentation

### Core Documentation

- [Project Context](/docs/CONTEXT.md) - Overview and business requirements
- [System Architecture](/docs/ARCHITECTURE.md) - Technical architecture and components
- [API Documentation](/docs/API.md) - API endpoints and usage
- [Roadmap](/docs/ROADMAP.md) - Future development plans
- [Changelog](/docs/CHANGELOG.md) - Version history and changes

### Development Resources

- [Development Guide](/docs/DEVELOPMENT.md) - Setup and contribution guidelines
- [Code Style Guide](/docs/STYLE_GUIDE.md) - Coding standards and best practices
- [Testing Guide](/docs/TESTING.md) - Testing strategies and guidelines
- [Deployment Guide](/docs/DEPLOYMENT.md) - Deployment instructions

### Tools and Workflows

- [VS Code Setup](/docs/VSCODE_SETUP.md) - Recommended extensions and settings
- [Git Workflow](/docs/GIT_WORKFLOW.md) - Branching strategy and PR guidelines
- [Troubleshooting](/docs/TROUBLESHOOTING.md) - Common issues and solutions

## 🏗 Project Structure

```
.
├── android/              # Android platform-specific code
├── assets/               # Images, fonts, etc.
├── ios/                  # iOS platform-specific code
├── lib/                  # Main application code
│   ├── core/             # Core functionality
│   │   ├── constants/    # App-wide constants
│   │   ├── errors/       # Error handling
│   │   ├── services/     # Core services
│   │   ├── theme/        # App theming
│   │   └── utils/        # Utility functions
│   │
│   ├── features/       # Feature modules
│   │   ├── auth/         # Authentication
│   │   ├── vehicles/     # Vehicle listings
│   │   ├── bookings/     # Booking management
│   │   ├── payments/     # Payment processing
│   │   ├── messages/     # In-app messaging
│   │   └── profile/      # User profiles
│   │
│   ├── shared/         # Shared components
│   │   ├── widgets/      # Reusable widgets
│   │   └── models/       # Shared models
│   │
│   └── main.dart      # Application entry point
│
├── test/               # Unit and widget tests
├── integration_test/     # Integration tests
├── web/                  # Web-specific code
├── .github/              # GitHub workflows
├── docs/                 # Documentation
├── scripts/              # Utility scripts
├── .env.example          # Example environment variables
├── pubspec.yaml          # Dependencies
└── README.md             # This file
```

## 🛠 Development Scripts

- `setup.sh` - Sets up the development environment
- `scripts/format.sh` - Formats all Dart code
- `scripts/analyze.sh` - Runs static analysis
- `scripts/test.sh` - Runs all tests
- `scripts/build.sh` - Builds the app for production

## 🔄 Development Workflow

1. Create a new branch for your feature/fix:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit them:
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

3. Push your changes and create a pull request:
   ```bash
   git push origin feature/your-feature-name
   ```

## 🌟 Features in Detail

### Authentication
- Email/password authentication
- Profile management
- Secure session handling
- Password reset flow

### Vehicle Management
- Vehicle listings with filters
- Image gallery
- Location-based search
- Availability calendar

### Booking System
- Real-time availability
- Booking management
- Cancellation policy
- Booking history

### Payments
- Khalti payment gateway
- Secure transaction handling
- Receipt generation
- Refund processing

### Messaging
- Real-time chat
- Message status tracking
- Unread indicators
- Media sharing (future)

## 🛡️ Security

- All sensitive data is encrypted
- Secure API communication
- Regular security audits
- Compliance with data protection regulations

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

1. **Report Bugs**: File an issue with detailed steps to reproduce
2. **Suggest Features**: Share your ideas for new features
3. **Fix Issues**: Pick an issue and submit a pull request
4. **Improve Documentation**: Help make our docs better

Please read our [Contributing Guide](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 🚀 Deployment

### Web Deployment
```bash
# Build for production
flutter build web --release --dart-define-from-file=.env

# Deploy to Firebase Hosting (if configured)
firebase deploy --only hosting
```

### Mobile Deployment
Follow the official Flutter documentation for deploying to the [App Store](https://flutter.dev/docs/deployment/ios) and [Google Play](https://flutter.dev/docs/deployment/android).

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Flutter](https://flutter.dev/) - For the amazing cross-platform framework
- [Supabase](https://supabase.com/) - For the backend services
- [Khalti](https://khalti.com/) - For payment processing
- [Mapbox](https://www.mapbox.com/) - For mapping and location services
- The open-source community - For invaluable resources and libraries

## 📞 Support

For support, please:
- Check the [Troubleshooting Guide](/docs/TROUBLESHOOTING.md)
- Search existing issues
- Open a new issue if needed

## 📈 Analytics

We use Firebase Analytics to improve the app. No personally identifiable information is collected.

## 🔒 Security

Please report any security <NAME_EMAIL>. See our [Security Policy](SECURITY.md) for more information.
- [Progress](/docs/PROGRESS.md)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

For any queries, please contact [Your Email](mailto:<EMAIL>)

---

### Flutter Resources

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
